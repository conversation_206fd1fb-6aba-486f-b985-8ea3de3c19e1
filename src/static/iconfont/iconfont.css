@font-face {
  font-family: "iconfont"; /* Project id 4968699 */
  src: url('iconfont.woff2?t=1755676968727') format('woff2'),
       url('iconfont.woff?t=1755676968727') format('woff'),
       url('iconfont.ttf?t=1755676968727') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-player-play:before {
  content: "\e614";
}

.icon-play:before {
  content: "\e637";
}

.icon-edit:before {
  content: "\e645";
}

.icon-a-lujing52778:before {
  content: "\e607";
}

.icon-a-zu53509:before {
  content: "\e608";
}

.icon-a-lujing52775:before {
  content: "\e609";
}

.icon-a-lujing52770:before {
  content: "\e60a";
}

.icon-a-zu15832:before {
  content: "\e60b";
}

.icon-a-lujing52774:before {
  content: "\e60c";
}

.icon-a-zu53504:before {
  content: "\e60d";
}

.icon-a-zu15826:before {
  content: "\e60e";
}

.icon-a-lujing25668:before {
  content: "\e60f";
}

.icon-a-zu15837:before {
  content: "\e612";
}

