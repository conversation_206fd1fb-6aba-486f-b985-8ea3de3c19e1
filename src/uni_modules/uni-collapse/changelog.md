## 1.4.4（2024-03-20）
- 修复 titleBorder类型修正
## 1.4.3（2022-01-25）
- 修复 初始化的时候 ，open 属性失效的bug
## 1.4.2（2022-01-21）
- 修复 微信小程序resize后组件收起的bug
## 1.4.1（2021-11-22）
- 修复 vue3中个别scss变量无法找到的问题
## 1.4.0（2021-11-19）
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-collapse](https://uniapp.dcloud.io/component/uniui/uni-collapse)
## 1.3.3（2021-08-17）
- 优化 show-arrow 属性默认为true
## 1.3.2（2021-08-17）
- 新增 show-arrow 属性，控制是否显示右侧箭头
## 1.3.1（2021-07-30）
- 优化 vue3下小程序事件警告的问题
## 1.3.0（2021-07-30）
- 组件兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 1.2.2（2021-07-21）
- 修复 由1.2.0版本引起的 change 事件返回 undefined 的Bug
## 1.2.1（2021-07-21）
- 优化 组件示例
## 1.2.0（2021-07-21）
- 新增 组件折叠动画
- 新增 value\v-model 属性 ，动态修改面板折叠状态
- 新增 title 插槽 ，可定义面板标题
- 新增 border 属性 ，显示隐藏面板内容分隔线
- 新增 title-border 属性 ，显示隐藏面板标题分隔线
- 修复 resize 方法失效的Bug
- 修复 change 事件返回参数不正确的Bug
- 优化 H5、App 平台自动更具内容更新高度，无需调用 reszie() 方法
## 1.1.7（2021-05-12）
- 新增 组件示例地址
## 1.1.6（2021-02-05）
- 优化 组件引用关系，通过uni_modules引用组件
## 1.1.5（2021-02-05）
- 调整为uni_modules目录规范