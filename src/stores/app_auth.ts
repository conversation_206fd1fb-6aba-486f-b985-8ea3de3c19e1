import { defineStore } from 'pinia';
import { ref } from 'vue';
import { UniStorage } from './storage';
import dayjs from 'dayjs';

export const useAppAuthStore = defineStore(
  'app_auth',
  () => {
    // 注册时间
    const registerTime = ref();

    const setRegisterTime = () => {
      registerTime.value = Date.now();
    };

    const shouldShowAppAuthTime = () => {
      if (!registerTime.value) {
        setRegisterTime();
        uni.showModal({
          title: '提示',
          content: `欢迎使用本应用，您的授权有效期还剩 ${30} 天`,
          showCancel: false,
        });
      } else {
        const diffDays = dayjs().diff(dayjs(registerTime.value), 'day');
        if (diffDays > 30) {
          uni.showModal({
            title: '提示',
            content: `您的授权已过期，请联系管理员。点击确定将退出应用`,
            showCancel: false,
            success: function (res) {
              if (res.confirm) {
                plus.runtime.quit();
              }
            },
          });
        } else {
          uni.showModal({
            title: '提示',
            content: `您的授权有效期还剩 ${30 - diffDays} 天`,
            showCancel: false,
          });
        }
      }
    };

    return {
      registerTime,
      setRegisterTime,
      shouldShowAppAuthTime,
    };
  },
  {
    persist: {
      storage: UniStorage,
      key: 'tree_app_auth',
    },
  },
);
