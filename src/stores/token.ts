import { defineStore } from 'pinia';
import { ref } from 'vue';
import { UniStorage } from './storage';
import dayjs from 'dayjs';
export const useSurveyTokenStore = defineStore(
  'survey_token',
  () => {
    const accountNumber = ref('');
    const accessToken = ref('');
    const accessTokenExpires = ref(0);

    const setAccountNumber = (an: string) => {
      accountNumber.value = an;
    };

    const setAccessToken = (at: string) => {
      accessToken.value = at;
    };
    const setAccessTokenExpires = (ate: number) => {
      accessTokenExpires.value = ate;
    };

    const clearToken = () => {
      accountNumber.value = '';
      accessToken.value = '';
      accessTokenExpires.value = 0;
    };

    const checkIsExpires = (): boolean => {
      if (accessTokenExpires.value === 0) {
        return true; // 如果没有设置过期时间，认为已过期
      }
      return dayjs().isAfter(dayjs(accessTokenExpires.value));
    };

    return { accountNumber, accessToken, accessTokenExpires, setAccountNumber, setAccessToken, setAccessTokenExpires, clearToken, checkIsExpires };
  },
  {
    persist: {
      storage: UniStorage,
      key: 'tree_survey_token',
    },
  },
);
