<script lang="ts" setup>
import { useStatusBarHeight } from '@/hooks/useStatusBarHeight';
import { getAllSyncData, getSurveyList, isOffline } from '@/hooks/useSync';
import { goBack, uuid } from '@/utils';
import { ref, computed } from 'vue';
import { useGlobalStore } from '@/stores/global';
import { useSyncSurveyStore, type SyncItem } from '@/stores/sync_survey';
import { useSurveyTokenStore } from '@/stores/token';
import { storeToRefs } from 'pinia';
import { useSyncHistoryStore } from '@/stores/sync_history';
import dayjs from 'dayjs';
import UploadFailedPopup from './components/UploadFailedPopup.vue';
import { LogCat } from '@/utils/logger';

const surveyTokenStore = useSurveyTokenStore();
const { checkIsExpires } = surveyTokenStore;

const globalStore = useGlobalStore();
const { addSyncHistory } = useSyncHistoryStore();
const { dataSyncDownloadTime, dataSyncUploadTime } = storeToRefs(globalStore);
const syncSurveyStore = useSyncSurveyStore();
const { removeSyncItem, clearSyncList } = syncSurveyStore;
const { syncList } = storeToRefs(syncSurveyStore);
const { statusBarHeight } = useStatusBarHeight();

const uploadHasFailed = computed(() => syncList.value.some(item => item.status === 'error'));

// 上传失败数据popup相关
const showUploadFailedPopup = ref(false);

// 待上传数据选择相关
const selectedSyncIds = ref<Set<string>>(new Set());
const showUploadList = ref(false);

// 待上传的数据列表（只包含pending状态的）
const pendingSyncList = computed(() => {
  return syncList.value.filter(item => item.status === 'pending');
});

// 是否全选
const isAllSelected = computed(() => {
  return pendingSyncList.value.length > 0 && selectedSyncIds.value.size === pendingSyncList.value.length;
});

// 是否有选中项
const hasSelected = computed(() => {
  return selectedSyncIds.value.size > 0;
});

const syncDownloadTime = computed(() => {
  return dataSyncDownloadTime.value ? dayjs(dataSyncDownloadTime.value).format('YYYY-MM-DD HH:mm:ss') : '- -';
});

const syncUploadTime = computed(() => {
  return dataSyncUploadTime.value ? dayjs(dataSyncUploadTime.value).format('YYYY-MM-DD HH:mm:ss') : '- -';
});

const goSyncHistory = () => {
  uni.navigateTo({
    url: '/pages/sync_history/index',
  });
};

// 同步状态
const isDownloading = ref(false);
const isUploading = ref(false);
// 下载系统数据到本地
const handleDownloadData = async () => {
  if (isDownloading.value || isUploading.value) return;

  // 检查 token 是否过期
  if (checkIsExpires()) {
    uni.showModal({
      title: '授权已过期',
      content: '登录授权已过期，请重新登录',
      showCancel: false,
      success: function (res) {
        if (res.confirm) {
          uni.reLaunch({
            url: '/pages/login/index',
          });
        }
      },
    });
    return;
  }

  const nowTime = Date.now();
  try {
    isDownloading.value = true;

    const offline = await isOffline();
    if (offline) {
      uni.showToast({
        title: '当前处于离线状态，无法同步数据',
        icon: 'none',
      });
    }

    uni.showLoading({
      title: '正在同步数据...',
      mask: true,
    });
    const syncRes = await getAllSyncData();
    globalStore.setDataSyncDownloadTime(nowTime);
    uni.hideLoading();
    if (syncRes.every(item => item && item.statusCode === 200)) {
      uni.showToast({
        title: '数据同步成功',
        icon: 'success',
      });
      addSyncHistory({
        id: uuid(),
        createdAt: nowTime,
        type: 'DOWNLOAD',
        success: true,
      });
    } else {
      addSyncHistory({
        id: uuid(),
        createdAt: nowTime,
        type: 'DOWNLOAD',
        success: false,
      });
    }
  } catch (error) {
    console.log('同步error', error);
    addSyncHistory({
      id: uuid(),
      createdAt: nowTime,
      type: 'DOWNLOAD',
      success: false,
    });
    uni.hideLoading();
    uni.showToast({
      title: '同步失败，请重试',
      icon: 'none',
    });
  } finally {
    isDownloading.value = false;
  }
};

// 全选/反选
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedSyncIds.value.clear();
  } else {
    pendingSyncList.value.forEach(item => {
      selectedSyncIds.value.add(item.id);
    });
  }
};

// 切换单个选择状态
const toggleSelectItem = (itemId: string) => {
  if (selectedSyncIds.value.has(itemId)) {
    selectedSyncIds.value.delete(itemId);
  } else {
    selectedSyncIds.value.add(itemId);
  }
};

// 上传列表弹窗引用
const uploadListPopup = ref<any>(null);

// 显示上传列表
const showUploadListModal = () => {
  if (pendingSyncList.value.length === 0) {
    uni.showToast({
      title: '暂无待上传数据',
      icon: 'none',
    });
    return;
  }
  // 默认全选
  selectedSyncIds.value.clear();
  pendingSyncList.value.forEach(item => {
    selectedSyncIds.value.add(item.id);
  });
  uploadListPopup.value?.open('bottom');
};

// 关闭上传列表弹窗
const closeUploadListModal = () => {
  uploadListPopup.value?.close();
};

// 弹窗状态变化处理
const onUploadListPopupChange = (e: any) => {
  showUploadList.value = e.show;
};

// 上传本地数据
const handleUploadData = async () => {
  if (isDownloading.value || isUploading.value) return;

  // 检查 token 是否过期
  if (checkIsExpires()) {
    uni.showModal({
      title: '授权已过期',
      content: '登录授权已过期，请重新登录',
      showCancel: false,
      success: function (res) {
        if (res.confirm) {
          uni.reLaunch({
            url: '/pages/login/index',
          });
        }
      },
    });
    closeUploadListModal(); // 关闭选择列表
    return;
  }

  const nowTime = Date.now();
  isUploading.value = true;
  closeUploadListModal(); // 关闭选择列表

  // 只上传选中的数据
  const selectedItems = Array.from(selectedSyncIds.value);
  const uploadLength = selectedItems.length;

  // 开启屏幕常亮
  uni.setKeepScreenOn({
    keepScreenOn: true,
  });
  uni.showLoading({
    title: `正在上传选中的 ${uploadLength} 条数据`,
    mask: true,
  });

  try {
    const res = await syncSurveyStore.onHandleSyncUploadData(selectedItems);
    isUploading.value = false;
    uni.hideLoading();
    const { has401 } = res;

    // 计算上传结果统计
    const successCount = syncList.value.filter(item => selectedItems.includes(item.id) && item.status === 'success').length;
    const errorCount = syncList.value.filter(item => selectedItems.includes(item.id) && item.status === 'error').length;

    globalStore.setDataSyncUploadTime(nowTime);
    // 屏幕常亮关闭
    uni.setKeepScreenOn({
      keepScreenOn: false,
    });
    if (has401) {
      addSyncHistory({
        id: uuid(),
        createdAt: nowTime,
        type: 'UPLOAD',
        success: false,
      });
      const noUploadLength = uploadLength - successCount - errorCount;
      uni.showModal({
        title: '授权已过期，请重新登录',
        showCancel: false,
        content: `当前上传成功 ${successCount} 条，上传失败 ${errorCount} 条，待上传数据 ${noUploadLength} 条`,
        success: function (res) {
          if (res.confirm) {
            uni.reLaunch({
              url: '/pages/login/index',
            });
          }
        },
      });
      // TODO: 有成功的  但是获取列表咋办？
    } else {
      if (errorCount > 0) {
        uni.showToast({
          title: `上传成功 ${successCount} 条，上传失败 ${errorCount} 条`,
          icon: 'none',
          duration: 3000,
        });
        addSyncHistory({
          id: uuid(),
          createdAt: nowTime,
          type: 'UPLOAD',
          success: false,
        });

        // 弹出失败数据弹窗
        openFailedPopup();

        if (successCount > 0) {
          await getSurveyList();
        }
        return;
      } else {
        await getSurveyList();
        addSyncHistory({
          id: uuid(),
          createdAt: nowTime,
          type: 'UPLOAD',
          success: true,
        });
        uni.showToast({
          title: `成功上传 ${successCount} 条数据`,
          icon: 'none',
        });
        // 清空选择
        selectedSyncIds.value.clear();
      }
    }
  } catch (error) {
    // 屏幕常亮关闭
    uni.setKeepScreenOn({
      keepScreenOn: false,
    });
    LogCat.info(`同步数据try catch的error：`, error);
    addSyncHistory({
      id: uuid(),
      createdAt: nowTime,
      type: 'UPLOAD',
      success: false,
    });
    isUploading.value = false;
    uni.hideLoading();
    uni.showToast({
      title: '上传失败，请重试',
      icon: 'none',
    });
  }
};

const openFailedPopup = () => {
  showUploadFailedPopup.value = true;
};

// 处理单个数据删除
const handleSingleDelete = (item: SyncItem) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除此条失败数据吗？',
    success: res => {
      if (res.confirm) {
        removeSyncItem(item.id);
        shouldHideFailedPopup();
        uni.showToast({
          title: '删除成功',
          icon: 'success',
        });
      }
    },
  });
};

// 处理批量覆盖失败数据
const handleBatchCover = () => {
  uni.showModal({
    title: '确认批量覆盖',
    content: `确定要覆盖所有上传失败的数据吗？`,
    success: res => {
      if (res.confirm) {
        // TODO: 实现批量覆盖逻辑
      }
    },
  });
};

// 处理批量删除失败数据
const handleBatchDelete = () => {
  uni.showModal({
    title: '确认批量删除',
    content: `确定要删除所有上传失败的数据吗？`,
    success: res => {
      if (res.confirm) {
        clearSyncList();
        shouldHideFailedPopup();
        uni.showToast({
          title: '删除成功',
          icon: 'success',
        });
      }
    },
  });
};

const shouldHideFailedPopup = () => {
  if (syncList.value.every(item => item.status !== 'error')) {
    showUploadFailedPopup.value = false;
  }
};
</script>

<template>
  <view class="w-full h-full flex flex-col bg-gradient-to-b from-#c4eee6 to-#fff">
    <view
      class="w-full"
      :style="{
        paddingTop: statusBarHeight + 12 + 'px',
      }"
    >
      <view class="flex items-center gap-10px px-10px py-24px">
        <view class="w-auto flex items-center" @click="goBack">
          <uni-icons type="left" size="30" color="#333"></uni-icons>
          <text class="text-#333 text-18px font-bold">数据同步中心</text>
        </view>
      </view>
    </view>

    <view class="w-full flex-1 flex flex-col px-18px gap-20px">
      <!-- 上传数据 -->
      <view
        class="w-full flex flex-col px-12px pb-12px rounded-8px b-(2px solid #fff) shadow-[0_3px_6px_1px_rgba(0,0,0,0.08)] bg-gradient-to-b from-#d4f8ea to-#ebfef8"
      >
        <view class="w-full flex justify-between items-center pt-12px">
          <text class="text-#333 text-17px font-bold">上传本地数据</text>
          <text class="text-#333 text-17px font-bold">{{ syncList.length }} / 0</text>
        </view>
        <text class="py-12px text-#516280 text-14px">将本地数据同步到系统服务器</text>
        <view class="text-#516280 flex items-center gap-5px pb-12px">
          <uni-icons type="loop" size="16" color="#08bd92"></uni-icons>
          <text class="text-12px">{{ syncUploadTime }}</text>
        </view>
        <!-- 选择上传数据按钮 - 只要有待上传数据就显示 -->
        <button
          v-if="pendingSyncList.length > 0"
          :disabled="isUploading"
          class="w-full h-44px fc text-#fff bgPrimary mb-8px"
          :loading="isUploading"
          @click="showUploadListModal"
        >
          选择上传数据
        </button>

        <!-- 上传失败按钮 - 有失败数据时显示 -->
        <view v-if="uploadHasFailed" class="w-full flex flex-col">
          <view class="text-14px text-#225ed5 decoration-underline text-#ff6a6a fc pt-15px" @click="openFailedPopup"> 查看失败数据 </view>
        </view>

        <!-- 无数据状态 - 既没有待上传数据也没有失败数据时显示 -->
        <button v-if="!pendingSyncList.length && !uploadHasFailed" class="w-full h-44px fc text-#fff bgPrimary mb-8px">暂无待上传数据</button>
      </view>

      <!-- 下载数据 -->
      <view
        class="w-full flex flex-col px-12px pb-12px rounded-8px b-(2px solid #fff) shadow-[0_3px_6px_1px_rgba(0,0,0,0.08)] bg-gradient-to-b from-#d4f8ea to-#ebfef8"
      >
        <view class="w-full flex justify-between items-center pt-12px">
          <text class="text-#333 text-17px font-bold">同步系统数据</text>
          <view class="items-center">
            <!-- <uni-data-checkbox multiple v-model="isDownloadMedia" :localdata="[{ text: '视频、图片', value: 1 }]" /> -->
          </view>
        </view>
        <text class="py-12px text-#516280 text-14px">同步最新数据到本地</text>
        <text class="pb-12px text-#516280 text-14px">注：为保证数据精确，请先上传本地数据</text>
        <view class="text-#516280 flex items-center gap-5px pb-12px">
          <uni-icons type="loop" size="16" color="#225ed5"></uni-icons>
          <text class="text-12px">{{ syncDownloadTime }}</text>
        </view>
        <button class="w-full h-44px fc text-#fff bg-#225ed5" @click="handleDownloadData" :loading="isDownloading" :disabled="!!pendingSyncList.length">
          开始同步
        </button>
      </view>

      <!-- 同步历史 -->
      <view class="w-full flex items-center gap-10px px-20px rounded-8px bg-#fff h-50px shadow-[rgba(0_3px_6px_1px_rgba(0,0,0,0.08))]" @click="goSyncHistory">
        <image src="@/static/images/icons/sync_icon.png" class="w-18px h-18px" mode="widthFix"></image>
        <text class="text-#333">同步历史</text>
        <uni-icons class="ml-auto" type="right" size="16" color="#333"></uni-icons>
      </view>
    </view>

    <!-- 上传失败数据弹窗 -->
    <UploadFailedPopup
      v-model:visible="showUploadFailedPopup"
      @handle-single-delete="handleSingleDelete"
      @handle-batch-cover="handleBatchCover"
      @handle-batch-delete="handleBatchDelete"
    />

    <!-- 上传数据选择弹窗 -->
    <uni-popup ref="uploadListPopup" type="share" background-color="#fff" @change="onUploadListPopupChange">
      <view class="w-full h-70vh overflow-hidden flex flex-col">
        <!-- 标题栏 -->
        <view class="w-full px-15px py-12px border-b border-#eee flex items-center justify-between">
          <text class="text-18px font-bold text-#333">选择上传数据</text>
          <view class="flex items-center gap-10px">
            <text class="text-14px text-#666">{{ selectedSyncIds.size }}/{{ pendingSyncList.length }}</text>
            <view class="w-70px fc py-6px text-12px rounded-4px border bgPrimary text-#fff" @click="toggleSelectAll">
              {{ isAllSelected ? '取消全选' : '全选' }}
            </view>
          </view>
        </view>

        <!-- 待上传数据列表 -->
        <view class="flex-1 overflow-hidden">
          <scroll-view :scroll-y="true" class="h-full px-15px">
            <view v-for="item in pendingSyncList" :key="item.id" class="py-12px border-b border-#eee">
              <view class="flex items-center gap-12px" @click="toggleSelectItem(item.id)">
                <!-- 选择框 -->
                <view
                  class="w-20px h-20px rounded-4px border-2 flex items-center justify-center"
                  :class="selectedSyncIds.has(item.id) ? 'border-#08bd92 bg-#08bd92' : 'border-#ccc bg-#f5f5f5'"
                >
                  <uni-icons v-if="selectedSyncIds.has(item.id)" type="checkmarkempty" size="14" color="#fff" />
                </view>

                <!-- 数据信息 -->
                <view class="flex-1">
                  <text class="text-16px font-bold text-#333">
                    {{ item.data.treeCode || '无编号' }}
                  </text>
                  <view class="text-12px text-#666 mt-2px">
                    {{ item.data.treeSpecies || '未知树种' }}
                  </view>
                </view>
              </view>
            </view>

            <!-- 空状态 -->
            <view v-if="pendingSyncList.length === 0" class="py-40px text-center text-#999"> 暂无待上传数据 </view>
          </scroll-view>
        </view>

        <!-- 底部按钮 -->
        <view class="w-full px-15px py-12px bg-white border-t border-#eee flex items-center gap-12px">
          <button class="flex-1 h-44px text-#666 bg-#f5f5f5 text-16px rounded-5px fc font-bold" @click="closeUploadListModal">取消</button>
          <button class="flex-1 h-44px text-#fff bgPrimary text-16px rounded-5px fc font-bold" :disabled="!hasSelected" @click="handleUploadData">
            开始上传 ({{ selectedSyncIds.size }})
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<style>
uni-button[disabled] {
  background-color: #abb6ce !important;
  color: #fff !important;
}
</style>
