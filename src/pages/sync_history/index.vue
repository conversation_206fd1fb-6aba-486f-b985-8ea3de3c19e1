<script lang="ts" setup>
import { useStatusBarHeight } from '@/hooks/useStatusBarHeight';
import { goBack } from '@/utils';
import { useSyncHistoryStore } from '@/stores/sync_history';
import { storeToRefs } from 'pinia';
import dayjs from 'dayjs';
import { computed } from 'vue';

const { statusBarHeight } = useStatusBarHeight();
const syncHistoryStore = useSyncHistoryStore();
const { syncHistory } = storeToRefs(syncHistoryStore);

// sortByCreatedAt

const syncHistorySorted = computed(() => {
  return syncHistory.value.sort((a, b) => b.createdAt - a.createdAt);
});
</script>

<template>
  <view
    class="w-full h-full flex flex-col bg-gradient-to-b from-#08bd92 to-#07a47f"
  >
    <!-- 顶部统计区域 -->
    <view
      class="w-full"
      :style="{
        paddingTop: statusBarHeight + 12 + 'px',
      }"
    >
      <view class="flex items-center gap-10px px-10px py-24px">
        <view class="w-auto flex items-center" @click="goBack">
          <uni-icons type="left" size="30" color="#fff"></uni-icons>
          <text class="text-#fff text-18px font-bold">同步历史</text>
        </view>
      </view>
    </view>
    <view class="flex-1 overflow-hidden bg-#fff">
      <scroll-view class="h-full" scroll-y v-if="syncHistorySorted.length">
        <view
          class="w-full h-50px px-20px flex items-center b-b-(1px solid #f2f2f2)"
          v-for="item in syncHistorySorted"
          :key="item.id"
        >
          <text>{{ dayjs(item.createdAt).format('YYYY-MM-DD HH:mm:ss') }}</text>
          <text v-if="item.type === 'UPLOAD'" class="ml-10px text-#08bd92">
            上传
          </text>
          <text v-if="item.type === 'DOWNLOAD'" class="ml-10px text-#225ed5">
            同步
          </text>

          <uni-icons
            class="ml-auto"
            type="checkmarkempty"
            size="20"
            color="#08bd92"
            v-if="item.success"
          />
          <uni-icons
            class="ml-auto"
            type="closeempty"
            size="20"
            color="#ff4e4e"
            v-else
          />
        </view>
      </scroll-view>
      <view class="w-full px-20px" v-else>
        <view
          class="w-full min-h-400px fc mt-20px rounded-8px text-#555 text-14px b-(1px dashed #ccc)"
        >
          暂无同步数据
        </view>
      </view>
    </view>
  </view>
</template>
