<script lang="ts" setup>
import { ref } from 'vue';
import { useSyncSurveyStore } from '@/stores/sync_survey';
import { useSurveyTokenStore } from '@/stores/token';
import { useLocationStore } from '@/stores/location';
import { useStatusBarHeight } from '@/hooks/useStatusBarHeight';
import { storeToRefs } from 'pinia';

const syncSurveyStore = useSyncSurveyStore();
const { statusBarHeight } = useStatusBarHeight();
const { syncList } = storeToRefs(syncSurveyStore);

const surveyTokenStore = useSurveyTokenStore();
const { clearToken } = surveyTokenStore;
const { accountNumber } = storeToRefs(surveyTokenStore);

const locationStore = useLocationStore();
const { getCurrentLocationModeName } = locationStore;

// 退出登录确认弹窗引用
const logoutConfirmRef = ref(null);

const goSync = () => {
  if (!accountNumber.value) {
    uni.showToast({
      title: '请先登录',
      icon: 'none',
      duration: 1500,
      success: () => {
        goLogin();
      },
    });
    return;
  }
  uni.navigateTo({
    url: '/pages/sync/index',
  });
};
const goCache = () => {
  uni.navigateTo({
    url: '/pages/map-cache/index',
  });
};

// 打开退出登录确认弹窗
const openLogoutConfirm = () => {
  if (logoutConfirmRef.value) {
    // @ts-ignore
    logoutConfirmRef.value.open();
  }
};

// 执行退出登录操作
const handleLogout = () => {
  try {
    // 清除存储的用户数据（参考login页面中存储的数据）
    clearToken();

    uni.showToast({
      title: '退出成功',
      icon: 'success',
    });

    uni.navigateTo({
      url: '/pages/login/index',
    });
  } catch (e) {
    console.error('退出登录失败:', e);
    uni.showToast({
      title: '退出失败，请重试',
      icon: 'none',
    });
  } finally {
    // 关闭确认弹窗
    if (logoutConfirmRef.value) {
      // @ts-ignore
      logoutConfirmRef.value.close();
    }
  }
};

const goLogin = () => {
  if (!accountNumber.value) {
    uni.navigateTo({
      url: '/pages/login/index',
    });
  }
};

let debuggerNumberCount = 0;
let debuggerTimer: ReturnType<typeof setTimeout> | null = null;

const goDebugger = () => {
  // 清除之前的定时器
  if (debuggerTimer !== null) {
    clearTimeout(debuggerTimer);
  }

  // 设置新的3秒定时器
  debuggerTimer = setTimeout(() => {
    debuggerNumberCount = 0;
    debuggerTimer = null;
  }, 3000);

  // 累加点击次数
  debuggerNumberCount++;

  // 达到条件时跳转
  if (debuggerNumberCount > 10) {
    debuggerNumberCount = 0;
    if (debuggerTimer !== null) {
      clearTimeout(debuggerTimer);
      debuggerTimer = null;
    }
    uni.navigateTo({
      url: '/pages/debugger/index',
    });
  }
};

const goMySurvey = () => {
  uni.navigateTo({
    url: '/pages/survey_my/index',
  });
};

const goLocationSettings = () => {
  uni.navigateTo({
    url: '/pages/location_settings/index',
  });
};
</script>

<template>
  <view class="w-full h-full bg-gray-50 flex flex-col">
    <!-- 用户信息区域 -->
    <view
      class="w-full bg-gradient-to-b from-#08bd92 to-#07a47f px-4 pb-60px"
      :style="{
        paddingTop: statusBarHeight + 50 + 'px',
      }"
    >
      <view class="flex items-center" @click="goLogin">
        <image src="@/static/images/icons/user.png" mode="widthFix" class="w-75px"></image>
        <view class="ml-15px">
          <text class="text-lg font-medium text-#fff">
            {{ accountNumber ? accountNumber : '游客' }}
          </text>
          <view class="text-sm text-#eee flex items-center mt-1 gap-5px ml-2px">
            <text v-if="!accountNumber">去登录</text>
            <text v-else>欢迎您</text>
          </view>
        </view>
      </view>
    </view>
    <view class="relative">
      <view class="flex items-center text-#fff p-10px w-90% ml-5% absolute top--40px bg-#fff b-rd-15px">
        <view class="flex-1 flex items-center gap-10px">
          <image src="/static/images/icons/xxzx.png" mode="widthFix" class="w-70px h-70px"></image>
          <!-- TODO: 调查中数据为离线缓存数据 -->
          <view class="flex flex-col">
            <text class="text-17px font-bold text-#516280">消息中心</text>
            <text class="text-12px text-#8B8AA1"> 新增<text class="color-#FE6C6D">2</text>条记录 </text>
          </view>
        </view>
        <view class="flex-1 flex items-center gap-10px" @click="goMySurvey">
          <image src="/static/images/icons/wddc.png" mode="widthFix" class="w-70px h-70px"></image>
          <view class="flex flex-col">
            <text class="text-17px font-bold text-#516280"> 我的调查</text>
            <text class="text-12px text-#8B8AA1"
              >共<text class="color-#FE6C6D">{{ syncList.length }}</text
              >条记录</text
            >
          </view>
        </view>
      </view>
    </view>

    <!-- 功能列表 -->
    <view class="w-94% bg-white mt-60px ml-3%">
      <uni-list :border="false">
        <!-- 待办事项 -->
        <!-- <uni-list-item :clickable="true" showArrow @click="navigateToTodo">
          <template v-slot:header>
            <view class="flex items-center">
              <text class="text-base text-#333">待办事项</text>
            </view>
          </template>
        </uni-list-item> -->

        <!-- 数据同步中心 -->
        <uni-list-item :clickable="true" showArrow @click="goSync">
          <template v-slot:header>
            <view class="w-full flex items-center">
              <image src="/static/images/icons/sjtbzx.png" mode="widthFix" class="w-22px"></image>
              <view class="flex items-center ml-15px">
                <text class="text-base text-#333">数据同步中心</text>
              </view>
            </view>
          </template>
        </uni-list-item>
        <!-- 定位方式 -->
        <uni-list-item :clickable="true" showArrow @click="goLocationSettings">
          <template v-slot:header>
            <view class="w-full flex items-center">
              <image src="/static/images/icons/dingwei.png" mode="widthFix" class="w-22px"></image>
              <view class="flex items-center ml-15px">
                <text class="text-base text-#333">定位方式</text>
                <text class="text-sm text-#999 ml-2">({{ getCurrentLocationModeName() }})</text>
              </view>
            </view>
          </template>
        </uni-list-item>
        <!-- <uni-list-item :clickable="true" showArrow @click="goCache">
          <template v-slot:header>
            <view class="w-full flex items-center">
              <image src="/static/images/icons/sjtbzx.png" mode="widthFix" class="w-22px"></image>
              <view class="flex items-center ml-15px">
                <text class="text-base text-#333">瓦片缓存管理</text>
              </view>
            </view>
          </template>
        </uni-list-item> -->
        <!-- TODO: 修改密码暂时放着 -->
        <!-- <uni-list-item :clickable="true" showArrow>
          <template v-slot:header>
            <image
              src="/static/images/icons/xgmm.png"
              mode="widthFix"
              class="w-26px h-26px"
            ></image>
            <view class="flex items-center ml-15px">
              <text class="text-base text-#333">修改密码</text>
            </view>
          </template>
        </uni-list-item> -->
        <uni-list-item :clickable="true" showArrow>
          <template v-slot:header>
            <view class="w-full flex items-center">
              <image src="/static/images/icons/gy.png" mode="widthFix" class="w-26px h-26px"></image>
              <view class="flex items-center ml-15px">
                <text class="text-base text-#333">关于</text>
              </view>
            </view>
          </template>
        </uni-list-item>
        <uni-list-item :clickable="true" @click="openLogoutConfirm" v-if="accountNumber">
          <template v-slot:header>
            <view class="w-full flex items-center">
              <image src="/static/images/icons/tcdl.png" mode="widthFix" class="w-22px"></image>
              <view class="flex items-center ml-15px">
                <text class="text-base text-#333">退出登录</text>
              </view>
            </view>
          </template>
        </uni-list-item>
      </uni-list>
    </view>
    <view class="w-full flex-1 relative">
      <view class="absolute bottom-0 right-0 w-100px h-100px" @click="goDebugger"> </view>
    </view>

    <!-- 退出登录确认弹窗 -->
    <uni-popup ref="logoutConfirmRef" type="dialog">
      <uni-popup-dialog type="warn" title="确认退出" content="确定要退出登录吗？" :before-close="false" @confirm="handleLogout"></uni-popup-dialog>
    </uni-popup>
  </view>
</template>
