<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useGlobalStore } from '@/stores/global';
import { getAllSyncData, getSurveyList, isOffline } from '@/hooks/useSync';
import { useSyncSurveyStore } from '@/stores/sync_survey';
import { storeToRefs } from 'pinia';
import dayjs from 'dayjs';

const globalStore = useGlobalStore();
const syncSurveyStore = useSyncSurveyStore();
const { syncList } = storeToRefs(syncSurveyStore);

const dataSyncDownloadTime = computed(() => {
  return globalStore.dataSyncDownloadTime
    ? dayjs(globalStore.dataSyncDownloadTime).format('YYYY-MM-DD HH:mm:ss')
    : '- -';
});

const dataSyncUploadTime = computed(() => {
  return globalStore.dataSyncUploadTime
    ? dayjs(globalStore.dataSyncUploadTime).format('YYYY-MM-DD HH:mm:ss')
    : '- -';
});

// 同步状态
const isDownloading = ref(false);
const isUploading = ref(false);

// 下载系统数据到本地
const handleDownloadData = async () => {
  if (isDownloading.value || isUploading.value) return;
  if (syncList.value.length) {
    uni.showToast({
      title: '为避免数据丢失，请先上传本地数据',
      icon: 'none',
    });
    return;
  }

  try {
    isDownloading.value = true;

    const offline = await isOffline();
    if (offline) {
      uni.showToast({
        title: '当前处于离线状态，无法同步数据',
        icon: 'none',
      });
    }

    uni.showLoading({
      title: '正在同步数据...',
      mask: true,
    });
    const syncRes = await getAllSyncData();
    uni.hideLoading();
    if (syncRes.every(item => item && item.statusCode === 200)) {
      uni.showToast({
        title: '数据同步成功',
        icon: 'success',
      });
    }
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: '同步失败，请重试',
      icon: 'none',
    });
  } finally {
    isDownloading.value = false;
  }
};

// 上传本地数据到系统
const handleUploadData = async () => {
  if (isDownloading.value || isUploading.value) return;

  if (!syncList.value.length) {
    uni.showToast({
      title: '暂无待上传数据',
      icon: 'none',
    });
    return;
  }

  try {
    isUploading.value = true;

    uni.showLoading({
      title: '正在上传数据...',
      mask: true,
    });
    const syncLength = await syncSurveyStore.onHandleSyncUploadData();
    if (syncLength) {
      uni.hideLoading();
      uni.showToast({
        title: '数据上传成功',
        icon: 'success',
      });
      isUploading.value = false;
      await getSurveyList();
    } else {
      isUploading.value = false;
      uni.hideLoading();
      uni.showToast({
        title: '数据上传失败，请重试',
        icon: 'none',
      });
    }
  } catch (error) {
    isUploading.value = false;
    uni.hideLoading();
    uni.showToast({
      title: '上传失败，请重试',
      icon: 'none',
    });
    console.error('上传数据失败:', error);
  } finally {
    isUploading.value = false;
  }
};
</script>

<template>
  <view class="w-full h-full bg-gray-50">
    <!-- 顶部状态区域 -->
    <view class="w-full bg-white px-4 py-6 mb-8 shadow-sm">
      <!-- 数据统计 -->
      <view class="flex justify-between">
        <view class="flex-1 text-center">
          <text class="text-24px font-bold text-#f3a73f block">{{
            syncList.length
          }}</text>
          <text class="text-14px text-#666666 mt-1">待上传数据</text>
        </view>
      </view>
    </view>

    <!-- 同步操作区域 -->
    <view class="w-full bg-white mb-4 shadow-sm mx-auto max-w-md">
      <!-- 下载数据 -->
      <view class="px-5 py-5 border-b border-b-solid border-#eee">
        <view class="flex items-center justify-between">
          <view class="flex items-center flex-1">
            <!-- <view class="w-12 h-12 rounded-full bg-#e6f7f2 fc mr-4">
              <uni-icons type="download" size="24" color="#01bd8d" />
            </view> -->
            <view class="flex flex-col">
              <text class="text-16px font-medium text-#333333 block"
                >同步系统数据</text
              >
              <text class="text-13px text-#666666 mt-1"
                >同步最新数据到本地</text
              >
              <text class="text-12px text-#999999 mt-1"
                >同步时间：{{ dataSyncDownloadTime }}</text
              >
            </view>
          </view>
          <button
            @click="handleDownloadData"
            :loading="isDownloading || isUploading"
            class="rounded-full text-14px font-medium border-none px-5 min-w-20 shadow-sm transition-all duration-300"
            :class="
              isDownloading
                ? 'bg-#ccc text-#999'
                : 'bg-#01bd8d text-white hover:bg-opacity-90'
            "
          >
            {{ isDownloading ? '同步中...' : '开始同步' }}
          </button>
        </view>
      </view>

      <!-- 上传数据 -->
      <view class="px-5 py-5">
        <view class="flex items-center justify-between">
          <view class="flex items-center flex-1">
            <!-- <view class="w-12 h-12 rounded-full bg-#fef6e9 fc mr-4">
              <uni-icons type="upload" size="24" color="#f3a73f" />
            </view> -->
            <view class="flex flex-col">
              <text class="text-16px font-medium text-#333333 block"
                >上传本地数据</text
              >
              <text class="text-13px text-#666666 mt-1"
                >将本地数据上传到系统服务器</text
              >
              <text class="text-12px text-#999999 mt-1"
                >上传时间：{{ dataSyncUploadTime }}</text
              >
            </view>
          </view>
          <button
            @click="handleUploadData"
            :loading="isDownloading || isUploading"
            class="rounded-full text-14px font-medium border-none px-5 min-w-20 shadow-sm transition-all duration-300"
            :class="
              isUploading
                ? 'bg-#ccc text-#999'
                : 'bg-#f3a73f text-white hover:bg-opacity-90'
            "
          >
            {{ isUploading ? '上传中...' : '开始上传' }}
          </button>
        </view>
      </view>
    </view>
  </view>
</template>
