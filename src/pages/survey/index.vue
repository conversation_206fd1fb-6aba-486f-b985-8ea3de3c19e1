<script lang="ts" setup>
import { ref, watch, computed, onMounted } from 'vue';
import BaseMap from './components/BaseMap.vue';
import LayerPopup from './components/LayerPopup.vue';
import TreeDetailPopup from './components/TreeDetailPopup.vue';
import DistrictSelector from './components/DistrictSelector.vue';

import { onHide, onLoad, onShow } from '@dcloudio/uni-app';
import { useGlobalStore } from '@/stores/global';
import { useSurveyListStore, type Survey } from '@/stores/survey_list';
import { useSurveyEditStore } from '@/stores/survey_edit';
import dayjs from 'dayjs';
import { storeToRefs } from 'pinia';
import { getLocationComplete, openGps, checkAndGuideForPreciseLocation } from '@/utils/location';
import { useSyncSurveyStore } from '@/stores/sync_survey';
import { formatInvestigateStatus } from '@/utils';
import MultiFilterPopup from './components/MultiFilterPopup.vue';
import { applySurveyFilters, type FilterOptions } from '@/utils/filterUtils';

const globalStore = useGlobalStore();
const surveyListStore = useSurveyListStore();
const { pannelInfo } = storeToRefs(surveyListStore);
const { clearCurrentEditData } = useSurveyEditStore();
const syncSurveyStore = useSyncSurveyStore();
const { syncList } = storeToRefs(syncSurveyStore);
const dataSyncDownloadTime = computed(() => {
  return globalStore.dataSyncDownloadTime ? dayjs(globalStore.dataSyncDownloadTime).format('YYYY-MM-DD HH:mm:ss') : '- -';
});

const currentLocation = ref({
  longitude: 0,
  latitude: 0,
});

// 定位loading状态
const isLocating = ref(false);

const layerPopupVisible = ref(false);
const showFloatingPannel = ref(false);
const currentLayer = ref('img');
const showTreeDetailPopup = ref(false); // 添加树木详情弹窗控制变量
const markers = ref<any[]>([]); // 地图标记点数据
const selectedTreeItem = ref<Survey | null>(null); // 当前选中的树木详情

// 行政区筛选相关
const selectedDistrict = ref('');
const currentDistrict = ref('行政区');
const showDistrictPopup = ref(false); // 添加行政区弹窗控制变量
const selectedGeometry = ref({});
const districtSelectorRef = ref<any>(null);

// BaseMap组件引用
const baseMapRef = ref<any>(null);

// 筛选相关状态
const showFilterPopup = ref(false);
const currentFilters = ref<FilterOptions>({
  surveyTimeRange: {
    startDate: '',
    endDate: '',
  },
  surveyStatus: [],
});

// 计算是否有筛选条件
const hasActiveFilters = computed(() => {
  return currentFilters.value.surveyTimeRange.startDate || currentFilters.value.surveyTimeRange.endDate || currentFilters.value.surveyStatus.length > 0;
});

// 筛选后的调查数据
const filteredSurveyList = computed(() => {
  return applySurveyFilters(surveyListStore.surveyList as Survey[], currentFilters.value, selectedGeometry.value);
});

const onLayerChange = (key: 'img' | 'vec') => {
  currentLayer.value = key;
};

const popup = ref<any>(null);
const projectPopup = ref<any>(null);

watch(layerPopupVisible, newVal => {
  if (newVal) {
    popup.value?.open('right');
  } else {
    popup.value?.close();
  }
});

watch(showFloatingPannel, newVal => {
  if (newVal) {
    projectPopup.value?.open('bottom');
  } else {
    projectPopup.value?.close();
  }
});

const onHandleCreate = async () => {
  // 清空pinia中的编辑数据
  clearCurrentEditData();

  uni.navigateTo({
    url: `/pages/survey_create/index?longitude=${currentLocation.value.longitude}&latitude=${currentLocation.value.latitude}`,
  });
};

// 监听筛选后的列表变化，重新渲染标记点
watch(
  () => filteredSurveyList.value,
  () => {
    renderMarkers();
  },
  { deep: true },
);

// 监听行政区选择变化，重新渲染标记点
watch(
  () => selectedGeometry.value,
  () => {
    renderMarkers();
  },
  { deep: true },
);

onLoad(() => {
  renderMarkers();
});

onMounted(async () => {
  // #ifdef APP-PLUS
  openGps();

  // 检查并引导用户开启精确位置权限
  setTimeout(async () => {
    await checkAndGuideForPreciseLocation();
  }, 500); // 缩短延迟时间
  // #endif
});

// 根据状态获取图标路径
const getIconUrl = (status: string) => {
  const mapedStatus = formatInvestigateStatus(status);
  switch (mapedStatus) {
    case '待调查':
      return 'static/images/icons/daidiaocha_map_marker.png'; // 待调查
    case '调查中':
      return 'static/images/icons/diaochazhong_map_marker.png'; // 调查中
    case '已调查':
      return 'static/images/icons/yidiaocha_map_marker.png'; // 已调查
    default:
      return 'static/images/icons/daidiaocha_map_marker.png';
  }
};
const firstLoad = ref(true);
onShow(() => {
  startLocationUpdate();
  if (firstLoad.value) {
    firstLoad.value = false;
    return;
  }
  // 页面首次加载，在BaseMap中startCompass，页面onShow阶段，BaseMap不会触发，所以要在这里加判断
  baseMapRef.value.startCompass();
});
/**
 * 开始定位更新 5s intelval
 */
const locationUpdateTimer = ref<any>(null);
const startLocationUpdate = () => {
  // #ifdef APP-PLUS
  locationUpdateTimer.value = setInterval(async () => {
    try {
      const location = await getLocationComplete();

      // 仅修改当前坐标位置
      if (baseMapRef.value) {
        baseMapRef.value.updateCurrentLocation({
          lat: location.latitude,
          lng: location.longitude,
        });
      }
    } catch (error) {
      console.error('定位失败：', error);
    }
  }, 1000 * 5);
  // #endif
};
onHide(() => {
  // #ifdef APP-PLUS
  locationUpdateTimer.value && clearInterval(locationUpdateTimer.value);
  baseMapRef.value.stopCompass();
  // #endif
});

// 渲染点位
const renderMarkers = () => {
  const markerData = filteredSurveyList.value
    .filter(item => item.location)
    .map(item => {
      const iconUrl = getIconUrl(item.investigateStatus);
      return {
        lat: item.location.y, // 纬度
        lng: item.location.x, // 经度
        iconUrl: iconUrl,
        data: item, // 传递完整的数据项
      };
    });

  markers.value = markerData;
  console.log('生成的标记点数据 - markers.value:', markers.value);
};

// 处理标记点击事件
const onMarkerClick = (treeData: Survey) => {
  console.log('父组件收到标记点击事件:', treeData);
  selectedTreeItem.value = treeData;
  showTreeDetailPopup.value = true;
};

const goList = (status?: string) => {
  const url = status ? `/pages/survey_list/index?status=${status}` : `/pages/survey_list/index`;
  uni.navigateTo({
    url,
  });
};

// 行政区选择相关方法
const openDistrictPopup = () => {
  districtSelectorRef.value?.openPopup();
};

// 清除行政区筛选
const clearDistrictFilter = () => {
  selectedDistrict.value = '';
  currentDistrict.value = '行政区';
  selectedGeometry.value = {}; // 清空geometry数据
};

const onMapMove = ({
  center,
}: {
  center: {
    lat: number;
    lng: number;
  };
}) => {
  const { lat, lng } = center;
  currentLocation.value = {
    latitude: Number(lat.toFixed(6)),
    longitude: Number(lng.toFixed(6)),
  };
};

/**
 * 定位到当前位置
 */
const onHandleLocation = async () => {
  if (isLocating.value) return; // 如果正在定位，直接返回

  try {
    isLocating.value = true; // 开始定位
    const location = await getLocationComplete();

    // 更新当前位置
    currentLocation.value = {
      latitude: location.latitude,
      longitude: location.longitude,
    };

    // 通知地图组件移动到当前位置
    if (baseMapRef.value) {
      baseMapRef.value.moveToLocation({
        lat: location.latitude,
        lng: location.longitude,
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('定位失败：', error);

    uni.showToast({
      title: '定位失败，请检查定位权限',
      icon: 'none',
    });
  } finally {
    isLocating.value = false; // 定位结束
  }
};

// 筛选相关方法
const openFilterPopup = () => {
  showFilterPopup.value = true;
};

const onFilterConfirm = (filters: FilterOptions) => {
  currentFilters.value = { ...filters };
  console.log('应用筛选条件:', filters);
};

const onFilterReset = () => {
  currentFilters.value = {
    surveyTimeRange: {
      startDate: '',
      endDate: '',
    },
    surveyStatus: [],
  };
  console.log('重置筛选条件');
};
</script>

<template>
  <view class="w-full h-full relative">
    <BaseMap
      ref="baseMapRef"
      :layer-type="currentLayer"
      :markers="markers"
      :geometry="selectedGeometry"
      @onMarkerClick="onMarkerClick"
      @onMapMove="onMapMove"
    />

    <view class="absolute left-0 top-38px w-full px-20px">
      <!-- 行政区筛选 -->
      <view class="absolute left-10px top-0 flex gap-10px justify-between items-center w-[calc(100%-20px)] h-45px z-1">
        <view class="flex-1 px-10px h-full flex items-center gap-10px bg-#fff shadow-[0px_0px_12px_1px_rgba(16,70,60,0.18)] rounded-[10px]">
          <span class="text-18px color-#000 iconfont icon-a-zu15826"></span>
          <view
            class="flex-1 h-full flex justify-start items-center pl-10px cursor-pointer text-18px"
            :class="selectedDistrict ? 'text-#333' : 'text-#6e7580'"
            @click="openDistrictPopup"
          >
            {{ currentDistrict }}
          </view>
          <!-- 清除按钮 -->
          <uni-icons v-if="selectedDistrict" type="close" size="24" color="#999" class="mr-8px cursor-pointer" @click="clearDistrictFilter"></uni-icons>
          <span v-else class="iconfont icon-a-zu15832 mr-8px !text-8px font-bold text-#00bf9f cursor-pointer" @click="openDistrictPopup"></span>
        </view>
        <!-- 筛选按钮 -->
        <view class="w-45px bg-#fff h-full fc flex-col fc gap-3px rounded-10px" @click="openFilterPopup">
          <image src="/static/images/icons/filter.png" mode="widthFix" class="w-14px" />
          <span class="text-10px"> 筛选 </span>
        </view>
      </view>

      <!-- 一级二级控制 -->
      <view class="absolute right-10px top-65px w-46px h-112px bg-#fff shadow-[0px_0px_12px_1px_rgba(16,70,60,0.18)] rounded-10px z-1 flex flex-col">
        <!-- TODO:  图层暂时注释-->
        <!-- <view
          class="flex-1 fc flex-col gap-3px border-rd-t-10px"
          @click="showFloatingPannel = true"
        >
          <image
            src="/static/images/icons/layer.png"
            mode="scaleToFill"
            class="w-20px h-20px"
          ></image>
          <span class="text-10px">图层</span>
        </view> -->
        <view class="flex-1 fc flex-col gap-3px border-rd-b-10px" @click="goList('')">
          <image src="/static/images/icons/list.png" mode="scaleToFill" class="w-20px h-20px"></image>
          <span class="text-10px">列表</span>
        </view>
        <view class="flex-1 fc flex-col gap-3px border-rd-b-10px" @click="onHandleCreate">
          <image src="/static/images/icons/add_tree.png" mode="scaleToFill" class="w-20px h-20px"></image>
          <text class="text-10px color-#0F1826">新增</text>
        </view>
      </view>

      <!-- 新增 -->
      <!-- <view
        class="absolute right-0 top-190px gap-3px w-46px h-55px bg-#fff shadow-[0px_0px_12px_1px_rgba(16,70,60,0.18)] rounded-10px z-9 flex flex-col items-center justify-center"
        @click="onHandleCreate"
      >
        <image
          src="/static/images/icons/add_tree.png"
          mode="scaleToFill"
          class="w-20px h-20px"
        ></image>
        <text class="text-10px color-#0F1826">新增</text>
      </view> -->
      <view
        class="absolute right-10px top-190px gap-3px w-46px h-46px bg-#fff shadow-[0px_0px_12px_1px_rgba(16,70,60,0.18)] rounded-10px z-9 flex flex-col items-center justify-center"
        @click="onHandleLocation"
      >
        <view v-if="isLocating" class="w-20px h-20px flex items-center justify-center">
          <uni-icons class="animate-spin text-#333" type="refreshempty" size="30"></uni-icons>
        </view>
        <image v-else src="/static/images/icons/dingwei.png" mode="scaleToFill" class="w-20px h-20px"></image>
      </view>
    </view>

    <view class="h-30px w-full fc absolute bottom-130px px-10px">
      <view class="w-full fc gap-10px h-30px rounded-8px bg-[rgba(0,0,0,0.5)]">
        <text class="text-#fff text-12px font-bold">经度: {{ currentLocation.longitude }}</text>
        <text class="text-#fff text-12px font-bold">纬度: {{ currentLocation.latitude }}</text>
      </view>
    </view>
    <view class="w-full h-100px absolute bottom-20px px-10px">
      <view class="w-full px-12px h-100px flex flex-col mx-auto bg-#fff rounded-14px">
        <view class="flex py-10px">
          <view class="flex-1 flex items-end gap-10px" @click="goList('待调查')">
            <image src="/static/images/icons/daidiaocha.png" mode="widthFix" class="w-44px h-44px"></image>
            <view class="flex flex-col">
              <text class="text-17px font-bold text-#333">{{ pannelInfo.daidiaochaCount }}</text>
              <text class="text-12px text-#8B8AA1">待调查</text>
            </view>
          </view>
          <view class="flex-1 flex items-center gap-10px" @click="goList('调查中')">
            <image src="/static/images/icons/diaochazhong.png" mode="widthFix" class="w-44px h-44px"></image>
            <view class="flex flex-col">
              <text class="text-17px font-bold text-#333">
                {{ pannelInfo.diaochazhongCount }}
              </text>
              <text class="text-12px text-#8B8AA1">调查中</text>
            </view>
          </view>
          <view class="flex-1 flex items-center gap-10px" @click="goList('已调查')">
            <image src="/static/images/icons/yidiaocha.png" mode="widthFix" class="w-44px h-44px"></image>
            <view class="flex flex-col">
              <text class="text-17px font-bold text-#333">{{ pannelInfo.yidiaochaCount }}</text>
              <text class="text-12px text-#8B8AA1">已调查</text>
            </view>
          </view>
        </view>

        <view class="w-full pt-10px b-t-1px b-t-solid b-t-#eee flex items-center">
          <view class="flex-1 flex items-center text-12px gap-5px">
            <image src="/static/images/icons/time.png" mode="widthFix" class="w-12px"></image>
            <text class="text-#8B8AA1">更新时间</text>
            <text class="text-#333">{{ dataSyncDownloadTime }}</text>
          </view>
          <view class="flex items-center text-12px gap-5px pr-10px">
            <image src="/static/images/icons/people.png" mode="widthFix" class="w-12px"></image>
            <text class="text-#8B8AA1">我的调查</text>
            <text class="text-#333">{{ syncList.length }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <uni-popup ref="popup" type="right" background-color="#fff" @change="(e: any) => (layerPopupVisible = e.show)">
    <LayerPopup @layer-change="onLayerChange" :active-layer="currentLayer" />
  </uni-popup>

  <!-- 树木详情弹窗 -->
  <TreeDetailPopup v-model:visible="showTreeDetailPopup" :tree-item="selectedTreeItem as Survey" />

  <!-- 行政区选择弹窗 -->
  <DistrictSelector
    ref="districtSelectorRef"
    :toggleBar="true"
    v-model:visible="showDistrictPopup"
    v-model:selectedDistrict="selectedDistrict"
    v-model:currentDistrict="currentDistrict"
    @update:selectedGeometry="selectedGeometry = $event"
  />

  <!-- 筛选弹窗 -->
  <MultiFilterPopup v-model:visible="showFilterPopup" @confirm="onFilterConfirm" @reset="onFilterReset" />
</template>

<style scoped>
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
