<script lang="ts" setup>
import { ref } from 'vue';
import tdtImage from '@/static/images/icons/tdt_image.png';
import tdtMap from '@/static/images/icons/tdt_map.png';

// 接收外部传入的当前图层类型
const props = defineProps({
  activeLayer: {
    type: String,
    default: 'img',
  },
});

const emit = defineEmits(['layer-change']);

const layerList = ref([
  {
    name: '天地图卫星地图',
    key: 'img',
    img: tdtImage,
  },
  {
    name: '天地图电子地图',
    key: 'vec',
    img: tdtMap,
  },
]);

const onLayerChange = (key: 'img' | 'vec') => {
  emit('layer-change', key);
};
</script>

<template>
  <view class="w-full h-full flex flex-col pt-30px px-10px">
    <!-- 底图切换 -->
    <view class="w-full">
      <view class="w-full h-25px flex items-center pl-8px text-12px">
        底图切换
      </view>
      <view class="w-full h-92px pl-18px pt-15px bg-#fff flex gap-16px">
        <view
          class="flex flex-col gap-6px"
          v-for="(item, index) in layerList"
          :key="index"
          @click="onLayerChange(item.key as 'img' | 'vec')"
        >
          <view class="w-64px h-50px relative">
            <img :src="item.img" class="w-64px h-50px border-rd-3px" />
            <view
              class="absolute left-0 top-0 w-64px h-50px bg-[rgba(15,24,38,.72)] b-rd-3px fc"
              v-if="activeLayer === item.key"
            >
              <img
                src="@/static/images/icons/checked.png"
                class="w-36px h-36px"
              />
            </view>
          </view>
          <text class="text-9px color-#000">{{ item.name }}</text>
        </view>
      </view>
    </view>
  </view>
</template>
