<template>
  <view class="w-full h-full relative">
    <view
      id="map"
      :markers="markers"
      :change:markers="renderjs.receiveMarkers"
      :geometry="geometry"
      :change:geometry="renderjs.receiveGeometry"
      :location="location"
      :change:location="renderjs.receiveLocation"
      :direction="direction"
      :change:direction="renderjs.receiveDirection"
      :currentLocation="currentLocation"
      :change:currentLocation="renderjs.receiveCurrentLocation"
      ref="mapContainer"
      class="renderjs w-full h-full z-0"
    ></view>
  </view>
</template>

<script>
export default {
  name: 'LeafletMap',
  emits: ['onMarkerClick', 'onMapMove'],
  props: {
    markers: {
      type: Array,
      default: () => [],
    },
    geometry: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      mapId: 'map-' + Date.now().toString() + Math.floor(Math.random() * 1000),
      location: {
        random: Math.random(),
        lat: 0,
        lng: 0,
      },
      currentLocation: {
        lat: 0,
        lng: 0,
      },
      direction: 0,
    };
  },
  methods: {
    updateCurrentLocation({ lat, lng }) {
      this.currentLocation = {
        lat,
        lng,
      };
    },
    moveToLocation({ lat, lng }) {
      this.location = {
        random: Math.random(),
        lat,
        lng,
      };
    },
    onMarkerClick(data) {
      this.$emit('onMarkerClick', data);
    },
    onMapMove({ center, zoom }) {
      this.$emit('onMapMove', { center, zoom });
    },
    // 获取地图中心点
    getMapCenter() {
      return new Promise(resolve => {
        // 创建一个临时的回调函数名
        const callbackName = 'mapCenterCallback_' + Date.now();

        // 在全局注册回调函数
        window[callbackName] = center => {
          resolve(center);
          // 清理回调函数
          delete window[callbackName];
        };

        // 调用renderjs方法
        this.$refs.mapContainer.getMapCenter(callbackName);
      });
    },

    compassCallback(res) {
      this.direction = res.direction;
    },

    // 启动罗盘
    startCompass() {
      uni.onCompassChange(this.compassCallback);

      // 启动罗盘监听
      uni.startCompass({
        success: () => {
          console.log('罗盘启动成功');
        },
        fail: error => {
          console.error('罗盘启动失败:', error);
          uni.showToast({
            title: '罗盘启动失败',
            icon: 'none',
          });
        },
      });
    },

    // 停止罗盘
    stopCompass() {
      // 停止罗盘监听
      uni.stopCompass({
        success: () => {
          console.log('罗盘停止成功');
        },
        fail: error => {
          console.error('罗盘停止失败:', error);
        },
      });

      // 移除罗盘变化监听
      uni.offCompassChange(this.compassCallback);
    },
  },
  mounted() {
    this.startCompass();
  },
};
</script>

<style scoped></style>

<script module="renderjs" lang="renderjs">
import L from 'leaflet';
import * as turf from '@turf/turf';

let mapInstance;
let crosshairLayer;
let locationMarker; // 当前位置标记

export default {
  data() {
    return {
      markersLayer: null,
      baseLayers: {},
      currentBaseLayer: null,
      geometryLayer: null,
      isFitMarker: false,
      currentDirection: 0,
    };
  },
  mounted() {
    this.link = document.createElement('link');
    this.link.rel = 'stylesheet';
    this.link.href = 'static/leaflet/dist/leaflet.css';
    document.head.appendChild(this.link);

    this.$nextTick(() => {
      this.initMap();
    });
  },
  methods: {
    receiveMarkers(newValue, oldValue, ownerVm, vm) {
      setTimeout(() => {
        this.updateMarkers(newValue);
      }, 10);
    },
    receiveGeometry(newValue, oldValue, ownerVm, vm) {
      this.updateGeometry(newValue);
    },
    receiveLocation(newValue, oldValue, ownerVm, vm) {
      if (newValue && newValue.lat && newValue.lng) {
        this.moveToLocation(newValue);
      }
    },
    receiveDirection(newValue, oldValue, ownerVm, vm) {
      if (mapInstance && newValue !== undefined) {
        this.updateLocationDirection(newValue);
      }
    },
    receiveCurrentLocation(newValue, oldValue, ownerVm, vm) {
      if (newValue && newValue.lat && newValue.lng) {
        // 更新当前位置坐标并创建/更新位置标记
        this.createLocationMarker(newValue.lat, newValue.lng, this.currentDirection || 0);
      }
    },
    initMap() {
      // 初始化地图
      const tiandituKey = '64a7440068a2bbc276c11927b54458f4';
      mapInstance = L.map('map', {
        zoomControl: false, // 禁用缩放控件
        attributionControl: false, // 禁用版权信息控件
        minZoom: 1,
        maxZoom: 25
      }).setView([44.103369, 82.136527], 12);

      // 添加天地图图层
      const token = '64a7440068a2bbc276c11927b54458f4';
      // 添加天地图影像底图
      const tiandituImg = L.tileLayer(
        'https://t{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=' +
          tiandituKey,
        {
          subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
          maxNativeZoom: 18,
          maxZoom: 25
        }
      );

      // 添加天地图影像注记
      const tiandituCia = L.tileLayer(
        'https://t{s}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=' +
          tiandituKey,
        {
          subdomains: ['0', '1', '2', '3', '4', '5', '6', '7'],
          maxNativeZoom: 18,
          maxZoom: 25
        }
      );

      const imgGroup = L.layerGroup([tiandituImg, tiandituCia]);
      imgGroup.addTo(mapInstance);

      // 设置初始图层
      // this.setBaseLayer(this.layerType);

      // 创建标记图层
      this.markersLayer = L.layerGroup().addTo(mapInstance);

      // 创建geometry图层
      this.geometryLayer = L.layerGroup().addTo(mapInstance);

      // 创建十字瞄准镜图层
      this.createCrosshair();

      // 添加官方比例尺控件到左上角位置
      const officialScaleControl = L.control.scale({
        position: 'topleft',
        metric: true,
        imperial: false,
      });
      officialScaleControl.addTo(mapInstance);
      officialScaleControl._container.style.left = "10px"
      officialScaleControl._container.style.top = "90px"
      officialScaleControl._container.style.marginLeft = "0"

      // 监听地图移动事件
      mapInstance.on('move', () => {
        const center = mapInstance.getCenter();
        const zoom = mapInstance.getZoom();
        this.$ownerInstance.callMethod('onMapMove', { center, zoom });
      });
    },

    setBaseLayer(type) {
      // 移除当前图层
      if (this.currentBaseLayer) {
        mapInstance.removeLayer(this.currentBaseLayer);
      }

      // 添加新图层
      this.currentBaseLayer = this.baseLayers[type];
      this.currentBaseLayer.addTo(mapInstance);
    },

    updateMarkers(markers) {
      // 清除现有标记
      this.markersLayer.clearLayers();

      if (!markers || markers.length === 0) {
        return;
      }

      // 添加新标记
      markers.forEach((marker, index) => {
        // 创建自定义图标
        let iconUrl = 'static/images/icons/daidiaocha_map_marker.png'; // 默认图标
        if (marker.iconUrl) {
          iconUrl = marker.iconUrl;
        }

        const customIcon = L.icon({
          iconUrl: iconUrl,
          iconSize: [32, 32], // 图标大小
          iconAnchor: [16, 16], // 图标锚点（底部中心）
        });
        const customIcons = L.divIcon({
          className: 'my-div-icon',
          html: `<div class="w-70px color-#fff ml--15px" style="text-shadow: -2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, 2px 2px 0 #000;">${marker.data.treeCode}</div><img class="w-32px h-32px" src="${iconUrl}" alt="">`,
          // iconSize: [32, 32], // 图标大小
          iconAnchor: [16, 32], // 图标锚点（底部中心）
        });
        const m = L.marker([marker.lat, marker.lng], {
          icon: customIcons,
        }).addTo(this.markersLayer);
        // 添加点击事件
        m.on('click', () => {
          console.log('Marker clicked in renderjs:', marker.data);
          this.$ownerInstance.callMethod('onMarkerClick', marker.data);
        });
      });

      // 使用turf库计算所有marker的边界并调整地图视图
      if(!this.isFitMarker){
        this.fitMarkersToView(markers);
        this.isFitMarker = true;
      }
    },

    // 使用turf库计算marker边界并调整地图视图
    fitMarkersToView(markers) {
      if (!markers || markers.length === 0) {
        return;
      }

      try {
        if (markers.length === 1) {
          // 只有一个marker时，直接定位到该点
          const marker = markers[0];
          mapInstance.setView([marker.lat, marker.lng], 16);
          return;
        }

        // 创建turf点集合
        const points = markers.map(marker =>
          turf.point([marker.lng, marker.lat])
        );

        // 创建点集合的FeatureCollection
        const pointCollection = turf.featureCollection(points);

        // 计算边界框
        const bbox = turf.bbox(pointCollection);

        // 将turf的bbox格式转换为Leaflet的bounds格式
        // turf bbox格式: [minX, minY, maxX, maxY] (经度, 纬度)
        // Leaflet bounds格式: [[minY, minX], [maxY, maxX]] (纬度, 经度)
        const bounds = [
          [bbox[1], bbox[0]], // 西南角 [minLat, minLng]
          [bbox[3], bbox[2]]  // 东北角 [maxLat, maxLng]
        ];

        // 调整地图视图以适应所有marker，添加适当的padding
        mapInstance.fitBounds(bounds, {
          padding: [50, 50], // 添加50像素的内边距
          maxZoom: 16 // 限制最大缩放级别，避免过度放大
        });

      } catch (error) {
        console.error('计算marker边界失败:', error);
        // 如果计算失败，使用传统方法
        this.fallbackFitMarkers(markers);
      }
    },

    // 备用方法：使用Leaflet原生方法计算边界
    fallbackFitMarkers(markers) {
      if (!markers || markers.length === 0) {
        return;
      }

      try {
        // 创建一个临时的LatLngBounds对象
        const group = new L.featureGroup();

        markers.forEach(marker => {
          const tempMarker = L.marker([marker.lat, marker.lng]);
          group.addLayer(tempMarker);
        });

        // 调整地图视图
        mapInstance.fitBounds(group.getBounds(), {
          padding: [50, 50],
          maxZoom: 16
        });

      } catch (error) {
        console.error('备用边界计算也失败:', error);
      }
    },

    updateGeometry(geometry) {
      // 清除现有geometry
      this.geometryLayer && this.geometryLayer.clearLayers();
      // 如果没有geometry数据或为空对象，直接返回
      if (!geometry || Object.keys(geometry).length === 0) {
        return;
      }

      try {
        // 创建GeoJSON图层
        const geoJsonLayer = L.geoJSON(geometry, {
          style: {
            color: '#01bd8d',
            weight: 2,
            opacity: 0.8,
            fillColor: '#01bd8d',
            fillOpacity: 0.2,
          },
        }).addTo(this.geometryLayer);

        // 自动缩放到geometry范围
        if (geoJsonLayer.getBounds().isValid()) {
          mapInstance.fitBounds(geoJsonLayer.getBounds(), {
            padding: [20, 20],
          });
        }
      } catch (error) {
        console.error('Error rendering geometry:', error);
      }
    },

    // 获取地图中心点
    getMapCenter(callbackName) {
      if (mapInstance) {
        const center = mapInstance.getCenter();
        const centerData = {
          longitude: center.lng,
          latitude: center.lat,
        };

        // 调用全局回调函数
        if (typeof callbackName === 'string' && window[callbackName]) {
          window[callbackName](centerData);
        }

        return centerData;
      }
      return null;
    },

    // 移动地图到指定位置
    moveToLocation(location) {
      if (mapInstance && location && location.lat && location.lng) {
        const currentZoom = mapInstance.getZoom();
        mapInstance.setView([location.lat, location.lng], currentZoom, {
          animate: true,
          duration: 1.0
        });

        this.createLocationMarker(location.lat, location.lng, this.currentDirection || 0);
      }
    },

    // 创建位置标记
    createLocationMarker(lat, lng, direction = 0) {
      // 创建当前位置图标 - 浅蓝色圆圈样式
      const locationIcon = L.divIcon({
        className: 'current-location-icon',
        html: `
          <div style="position: relative; width: 32px; height: 32px;">
            <!-- 方向箭头 -->
            <div style="
              position: absolute;
              top: 0px;
              left: 50%;
              width: 0;
              height: 0;
              border-left: 8px solid transparent;
              border-right: 8px solid transparent;
              border-bottom: 16px solid #4A90E2;
              transform: translateX(-50%) rotate(${direction}deg);
              transform-origin: 50% 100%;
              filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));
              z-index: 2;
            "></div>
            <!-- 主圆圈 -->
            <div style="
              position: absolute;
              top: 50%;
              left: 50%;
              width: 16px;
              height: 16px;
              background-color: #ffffff;
              border: 3px solid #4A90E2;
              border-radius: 50%;
              transform: translate(-50%, -50%);
              box-shadow: 0 2px 6px rgba(0,0,0,0.3);
              z-index: 3;
            "></div>
          </div>
        `,
        iconSize: [32, 32],
        iconAnchor: [16, 16],
      });

      // 如果已有位置标记，先移除
      if (locationMarker) {
        mapInstance.removeLayer(locationMarker);
      }

      // 添加当前位置标记
      locationMarker = L.marker([lat, lng], {
        icon: locationIcon,
        interactive: false, // 不可交互
        zIndexOffset: 900  // 确保在十字瞄准镜下方但在地图上方
      }).addTo(mapInstance);
    },

    // 更新位置标记的方向
    updateLocationDirection(direction) {
      this.currentDirection = direction;
      if (locationMarker) {
        const position = locationMarker.getLatLng();
        this.createLocationMarker(position.lat, position.lng, direction);
      }
    },

    // 创建十字瞄准镜
    createCrosshair() {
      // 创建十字瞄准镜图层
      crosshairLayer = L.layerGroup().addTo(mapInstance);

      // 获取地图容器的中心点
      const mapContainer = mapInstance.getContainer();
      const mapSize = mapInstance.getSize();
      const centerPoint = mapInstance.containerPointToLatLng([
        mapSize.x / 2,
        mapSize.y / 2,
      ]);

      // 创建十字瞄准镜的SVG图标
      const crosshairIcon = L.divIcon({
        className: 'crosshair-icon',
        html: `
          <svg width="30" height="30" style="pointer-events: none;">
            <line x1="15" y1="5" x2="15" y2="25" stroke="#ff0000" stroke-width="3" opacity="0.8" stroke-linecap="round"/>
            <line x1="5" y1="15" x2="25" y2="15" stroke="#ff0000" stroke-width="3" opacity="0.8" stroke-linecap="round"/>
          </svg>
        `,
        iconSize: [30, 30],
        iconAnchor: [15, 15],
      });

      // 创建十字瞄准镜标记
      const crosshairMarker = L.marker(centerPoint, {
        icon: crosshairIcon,
        interactive: false, // 不可交互
        zIndexOffset: 1000, // 确保在最上层
      });

      crosshairLayer.addLayer(crosshairMarker);

      // 监听地图移动事件，更新十字瞄准镜位置
      mapInstance.on('move zoom', () => {
        const newCenter = mapInstance.getCenter();
        crosshairMarker.setLatLng(newCenter);
      });
    },

  },
};
</script>
