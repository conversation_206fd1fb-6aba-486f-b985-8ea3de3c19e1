<script lang="ts" setup>
import { ref, computed, watch } from 'vue';

interface TagOption {
  text?: string;
  value: string;
  key?: string;
  diId?: number;
}

interface Props {
  title?: string;
  options: TagOption[];
  modelValue?: string;
  placeholder?: string;
  maxCustomLength?: number;
  allowCustom?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'confirm', value: string): void;
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {
  title: '选择',
  placeholder: '请选择或输入',
  maxCustomLength: 10,
  allowCustom: true,
});

const emit = defineEmits<Emits>();

// 内部状态
const selectedValue = ref(props.modelValue || '');
const customInput = ref('');

// 更新输入状态
const updateInputState = () => {
  const isPresetOption = props.options.some(option => option.value === selectedValue.value);
  if (!isPresetOption && selectedValue.value) {
    customInput.value = selectedValue.value;
  } else if (isPresetOption) {
    customInput.value = '';
  }
  // 始终显示自定义输入框
};
// 监听外部值变化
watch(
  () => props.modelValue,
  newValue => {
    selectedValue.value = newValue || '';
    updateInputState();
  },
  { immediate: true },
);

// 选择预设选项
const selectOption = (option: TagOption) => {
  selectedValue.value = option.value;
  customInput.value = ''; // 清空自定义输入
  updateValue();
};

// 自定义输入变化
const onCustomInput = (value: string) => {
  customInput.value = value;
  selectedValue.value = ''; // 清空预设选项的选中状态
  updateValue();
};

// 更新值
const updateValue = () => {
  const finalValue = customInput.value || selectedValue.value;
  emit('update:modelValue', finalValue);
};

// 确认选择
const handleConfirm = () => {
  const finalValue = customInput.value || selectedValue.value;
  emit('confirm', finalValue);
  emit('close');
};

// 取消选择
const handleCancel = () => {
  emit('close');
};

// 计算显示文本 (暂时不使用，保留以备后续需要)
const displayText = computed(() => {
  if (customInput.value) {
    return customInput.value;
  }
  const option = props.options.find(opt => opt.value === selectedValue.value);
  return option?.text || option?.key || option?.value || props.placeholder;
});
</script>

<template>
  <view class="bg-white rounded-t-20px max-h-80vh overflow-hidden">
    <!-- 头部 -->
    <view class="p-15px pb-10px b-b b-b-solid b-b-gray-200">
      <view class="flex items-center justify-between">
        <text class="text-18px font-500 text-gray-800">{{ title }}</text>
        <view class="flex items-center gap-20px">
          <text class="text-14px text-#555" @click="handleCancel">取消</text>
          <text class="text-14px bgPrimary px-20px py-5px text-#fff rounded-4px font-500" @click="handleConfirm">确定</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="p-20px max-h-60vh overflow-y-auto">
      <!-- 预设选项 -->
      <view class="flex flex-wrap gap-12px mb-30px">
        <view
          v-for="option in options"
          :key="option.value"
          class="px-16px py-8px bg-gray-200 rounded-4px transition-all duration-200"
          :class="{
            '!bg-#01bd8d': selectedValue === option.value && !customInput,
          }"
          @click="selectOption(option)"
        >
          <text
            class="text-14px text-gray-800"
            :class="{
              '!text-#fff': selectedValue === option.value && !customInput,
            }"
          >
            {{ option.text || option.key || option.value }}
          </text>
        </view>
      </view>

      <!-- 自定义输入区域 -->
      <view v-if="allowCustom" class="pt-10px b-t-(1px solid #eee)">
        <view class="flex items-center justify-between mb-12px">
          <text class="text-16px text-gray-800 font-500">其他：</text>
          <uni-easyinput
            :value="customInput"
            :placeholder="`不超过${maxCustomLength}个字`"
            :maxlength="maxCustomLength"
            :clearable="false"
            @input="onCustomInput"
            class="w-full"
          />
        </view>
      </view>
    </view>
  </view>
</template>
