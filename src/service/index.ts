// API基础URL配置
const BASE_URL = 'http://192.168.22.228:9390/proxy_api';
// const BASE_URL = 'http://47.92.103.246:9390/proxy_api';
// const BASE_URL = 'http://192.168.0.119:8080/api';
// const BASE_URL = "https://v6lmtb9d-5173.inc1.devtunnels.ms/proxy_api";
// const BASE_URL = "proxy_api";
import { LogCat } from '@/utils/logger';
import { useSurveyTokenStore } from '@/stores/token';

// 接口返回数据类型
export interface ApiResponse<T = any> {
  statusCode: number;
  data: T;
  [key: string]: any;
}

// Token刷新响应类型
export interface TokenResponse {
  accessToken: string;
  refreshToken: string;
}

// 请求方法类型
export type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE';

// 请求参数类型
export type RequestParams = Record<string, any>;

// 请求队列类型
type QueueCallback = () => void;

// uni请求结果类型+
interface UniRequestResult<T = any> {
  data: T;
  statusCode: number;
  header: Record<string, string>;
  cookies: string[];
}

// 存储等待中的请求队列
let requestQueue: QueueCallback[] = [];

// 是否正在刷新token
let isRefreshing: boolean = false;
let is401: boolean = false;

/**
 * 刷新token的方法
 * @returns {Promise<boolean>} 刷新结果
 */
async function refreshToken(): Promise<boolean> {
  try {
    const rToken = uni.getStorageSync('refreshToken');

    const [error, res] = (await uni.request({
      url: `${BASE_URL}/refresh_token`,
      method: 'POST',
      header: {
        'content-type': 'application/json',
      },
      data: {
        refreshToken: rToken,
      },
    })) as unknown as [any, UniRequestResult<TokenResponse>];

    if (!error && res.statusCode === 200 && res.data) {
      // 保存新的token
      uni.setStorageSync('accessToken', res.data.accessToken);
      uni.setStorageSync('refreshToken', res.data.refreshToken);
      return true;
    }
    return false;
  } catch (error) {
    console.error('刷新token失败:', error);
    return false;
  }
}

/**
 * 网络请求服务
 * @param {Object} options - 请求配置对象
 * @param {string} options.url - 请求地址
 * @param {RequestParams} [options.params={}] - 请求参数
 * @param {RequestMethod} [options.method='GET'] - 请求方式
 * @returns {Promise<ApiResponse>} 请求响应Promise
 */
export function service<T = any>({
  url,
  params = {},
  method = 'GET',
  header = {},
  dataType = 'json',
}: {
  url: string;
  params?: RequestParams | FormData;
  method?: RequestMethod;
  header?: Record<string, string>;
  dataType?: 'json' | 'String';
}): Promise<ApiResponse<T>> {
  return new Promise((resolve, reject) => {
    const surveyTokenStore = useSurveyTokenStore();
    let token = surveyTokenStore.accessToken;

    // 获取后端请求api地址
    const apiAddress = uni.getStorageSync('apiAddress') || '';
    let REAL_BASE_URL = apiAddress ? apiAddress : BASE_URL;

    const fullUrl = `${REAL_BASE_URL}${url}`;
    const headers: Record<string, string> = {
      'content-type': 'application/json',
      ...header,
      // TODO: 仅在开发环境
      // cookie:
      //   'tunnel_phishing_protection=fancy-horse-pvjs1rn.inc1; .Tunnels.Relay.WebForwarding.Cookies=CfDJ8Cs4yarcs6pKkdu0hlKHsZt-m6Wl4dFGYzRJcf2-bru6QKCkV_Zh6YVbA64TZp7Futk5ToUNF1TJ-2iIsh49vyHmwXzJq4Y7TdMAj1h-pdDD1-3drtXDeSmRL7aYa-bho4epmtOU3tcQzeP3KnHrclGkw_rsPTbGP6RTG8Wqv8p6rP-H-w1-E43E17ZUjZ-b_TeBpZjfnYx_jry3KHp2EvIWUUEvFbUw7914i-kKKMzRGvce7p3CzHUIiyPHqv5nZx6SK5okMKNN3-dfrdCo84foUxZ5ZFiFSQfCahe8wUkBXRmPqg_qjUGMluABewnVjHZIH96qE68HxNjEto77O90NQuGoMyYgm15CpQRonn8kfPW_o0yOW5GRgjmY-jlvqGKeAN1-KWFHUyFC1DOe4TQ41LGJpdqjhcMKdGAPYYBqDTAq7GB9mkSi24tW486gd1s-O-dRHqJztMOuI4uhOnkq26M7bNd5GqNZScRyS8K_6yptgVA6yRqYhCe_AJS1iERvUWQv7OGvuHfiB72yNqyNQM7clu-eICcRXOhfq5em9DwHTMNyXqiEf8t4zcz3rhFIzdv-P_CYwYBCF8dGS7tZzX6FTcD6Rz73JRQutlLHfbkNkKRwozzNHHhy-aURJQITIFOqC1aCUIp4KJ2fP8NHbBY1xOGJFLmkvYI4Q_mmN1Oiph9bVuFy7mQYNp24qQcRzTmAzZxwmLSF3RAM3WiLdX8dvK1ZrDotYFiq3lunFQL3iWGJUA0JKt4dWzOMjgbq7XhLWAR_IIony8OkuevXaAz_vDWLQ5K0-Htmc4uBw-rassWSvO1VdCM6E6XmkZlafjiEC2ZifyJPPPXNBhSLylMzaIk9kw7lz67rrOITX7s9jLFy8fV5Tv8P6Q4GzzGrbKk18YyiWmTd3x_78Pz_NwUVUsHNhcFlIWSuphyk6fxGD-RwwmrC81vFoCKmMIMrMk7LrQqA19kc4p8nVMO74DLLVd7kv7uvVFbXhJ8noNDs8Y40pCTDCeUTBXN0jy6BTxK6mLk9_Qsblj41mtpCBs__vytL0sbdyk7_hg90CuiXi02nEenWEsHFTSxaBvODKRm4lTsM6EdbajpJ32kZwWxqvNqTfBgroAmJETTeOdK4joGLTVoKmQOgBGu3cn0pWUCUxgz2d9MALn8toFtu8t8AJqx5W1WgpCALrHKhuMok0zh6KWK8tOGvdJnXzcu743TR5zRnXdzP8fkfhHxN2nY3ZyiCbC77N3SxwIgZAZeAHHl1njCumnxEsHPEz6UOar_4d1H9ydp1tATi-bWdr1AtnU2VfR2Z_Ng_Rn7RUxB4_P4vkb2WJX2YSBUgf2y64vg5-WPjvhEQyl8nBods_8XGmcBKduuxewzLLov3r5ziCvtDsuF3aiymlmF3_VIm2yUKwM1swQPSPe71eN4ndmNQJ1CRtqLLbBhR5f9nkHdlXk8k8vgh5LLWF1Wqumoem5z3vLL8uPZ_1EMTgo2fhEehv8Ee0FZ9ETkpitxK',
    };

    if (!url.includes('login') && !url.includes('change_password_for_expired') && token) {
      headers.authorization = `Bearer ${token}`;
    }

    const request = (): void => {
      LogCat.info(`[service]开始接口请求，URL: ${fullUrl}，参数：`, params);
      uni.request({
        url: fullUrl,
        data: params,
        timeout: 15 * 1000,
        method: method as any,
        header: headers,
        dataType,
        success: (res: any) => {
          LogCat.info(`[service]接口请求成功，URL: ${fullUrl}`);
          resolve(res);
        },
        fail: err => {
          try {
            LogCat.error(`[service]接口请求失败，URL: ${fullUrl}，错误信息：`, err);
          } catch (e) {}
          uni.showToast({
            title: '请求失败，请检查网络状态！',
            icon: 'none',
          });
          reject(err);
        },
        complete: async (res: any) => {
          // 处理认证失败
          if (res.statusCode === 401) {
            if (url.includes('login')) {
              uni.showToast({
                title: '账号或密码错误',
                icon: 'none',
              });
              return;
            }
            if (!is401) {
              is401 = true;
              uni.showModal({
                title: '提示',
                showCancel: false,
                content: '授权已过期，请重新登录',
                success: function (res) {
                  if (res.confirm) {
                    is401 = false;
                    uni.reLaunch({
                      url: '/pages/login/index',
                    });
                  }
                },
              });
            }
          }

          // 处理服务器错误
          if (res.statusCode === 500) {
            uni.showToast({
              title: '服务器错误，请稍后再试',
              icon: 'none',
            });
            reject(res);
          }
        },
      });
    };

    request();
  });
}

/**
 * 文件上传服务
 * @param {Object} options - 上传配置对象
 * @param {string} options.url - 上传地址
 * @param {Array} options.files - 需要上传的文件数组对象，每个对象中包含name和filePath
 * @param {string} options.name - 文件对应的 key (仅在不使用files时生效)
 * @param {Record<string, any>} [options.formData={}] - 附加的表单数据
 * @param {Record<string, string>} [options.header={}] - 请求头
 * @returns {Promise<ApiResponse>} 上传响应Promise
 */
export function uploadService<T = any>({
  url,
  files,
  formData = {},
  header = {},
}: {
  url: string;
  files: UniApp.UploadFileOptionFiles[];
  formData?: Record<string, any>;
  header?: Record<string, string>;
}): Promise<ApiResponse<T>> {
  return new Promise((resolve, reject) => {
    const surveyTokenStore = useSurveyTokenStore();
    let token = surveyTokenStore.accessToken;

    // 获取后端请求api地址
    const apiAddress = uni.getStorageSync('apiAddress') || '';
    let REAL_BASE_URL = apiAddress ? apiAddress : BASE_URL;

    const fullUrl = `${REAL_BASE_URL}${url}`;
    const headers: Record<string, string> = {
      ...header,
    };

    if (!url.includes('login') && token) {
      headers.authorization = `Bearer ${token}`;
    }

    const upload = (): void => {
      LogCat.info(`[uploadService]开始文件上传，URL: ${fullUrl}，文件列表：`, files);
      LogCat.info(`[uploadService]开始文件上传，URL: ${fullUrl}，表单数据：`, formData);
      uni.uploadFile({
        url: fullUrl,
        files, // 直接传入files数组
        formData,
        header: headers,
        complete: async (res: any) => {
          if (res.statusCode === 200) {
            // TODO: 报错的重复校验测试 以及正常上传的 测试
            LogCat.info(`[uploadService-statusCode200]文件上传成功，URL: ${fullUrl}，响应数据：`, res);
            try {
              const data = JSON.parse(res.data);
              resolve({
                statusCode: res.statusCode,
                data,
              } as ApiResponse<T>);
            } catch (e) {
              resolve({
                statusCode: res.statusCode,
                data: res.data as any,
              } as ApiResponse<T>);
            }
          } else {
            LogCat.info(`[uploadService-complete]文件上传异常，URL: ${fullUrl}，响应数据：`, res);
            // 处理fail
            if (res.statusCode === 401) {
              resolve({
                statusCode: res.statusCode,
              });
            } else if (res.statusCode === 409) {
              try {
                const data = JSON.parse(res.data);
                resolve({
                  statusCode: res.statusCode,
                  data,
                } as ApiResponse<T>);
              } catch (error) {
                resolve({
                  statusCode: res.statusCode,
                  data: res.data as any,
                } as ApiResponse<T>);
              }
            } else if (res.statusCode === 500) {
              resolve({
                statusCode: res.statusCode,
                data: '当前数据异常，请重试',
              });
            } else if (res.statusCode === 404) {
              resolve({
                statusCode: res.statusCode,
                data: '服务器繁忙，请重试',
              });
            } else {
              resolve({
                statusCode: 403,
                data: '服务器未知错误，请重试或联系管理员',
              });
            }
          }
        },
      });
    };

    upload();
  });
}
