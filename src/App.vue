<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app';
import { useGlobalStore } from '@/stores/global';
import { LogCat } from '@/utils/logger';
// import { useAppAuthStore } from '@/stores/app_auth';

const globalStore = useGlobalStore();
const { setDefaultDictList } = globalStore;

// const appAuthStore = useAppAuthStore();

onLaunch(() => {
  // 锁定竖屏展示
  // #ifdef APP-PLUS
  plus.screen.lockOrientation('portrait-primary');
  // #endif

  // 初始化日志系统
  LogCat.init();

  setDefaultDictList();
  // appAuthStore.shouldShowAppAuthTime();
});
onShow(() => {
  console.log('App Show');
});
onHide(() => {
  console.log('App Hide');
});
</script>
<style>
uni-page-body {
  width: 100%;
  height: 100%;
}
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
}
</style>
