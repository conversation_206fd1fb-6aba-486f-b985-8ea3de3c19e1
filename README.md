pinia 的安装

package.json 中先移除掉 pinia 跟持久化插件：pinia-plugin-persistedstate

再安装依赖

然后单独安装 npm install pinia pinia-plugin-persistedstate

再 npm i pinia@2.3.1 安装 uniapp 可用的 pinia 版本

这里是最终的版本情况：
"pinia": "^2.3.1",
"pinia-plugin-persistedstate": "^4.4.1",

离线缓存需要考虑多用户的问题

首次进入页面或者登录后，所有页面的离线数据先缓存下来？

根据 investigatorId 调查人 id 判断是否为已调查

首页底部定位到当前位置？

定位古树

证书密码
treeapp

证书别名
treeapp

移栽 TODO:

移栽任务部分，将完成按钮改为点亮 任务要求改为节点内容

移栽任务部分，完成移栽或者保存的时候，如果有未点亮的节点，给用户提示一下有未完成

移栽任务部分的列表中的状态为真实状态

完成移栽后，就不能再进行点亮节点以及修改了，这两个按钮都去掉

新增古树 叫新增

丰富首页筛选条件
行政区、时间段、调查状态

预览下载：已有下载记录 是否重新下载
下载：调用 byid 获取到 media , 遍历下载存入本地

拍摄的视频
{
"type": "video",
"tempFiles": [
{
"height": 1080,
"thumbTempFilePath": "/storage/emulated/0/Android/data/com.tree.app/apps/__UNI__753D3B4/temp/uni-media/video_thumb_1754386761246.jpg",
"fileType": "video",
"duration": 2.9,
"width": 1920,
"tempFilePath": "file:///storage/emulated/0/Android/data/com.tree.app/apps/__UNI__753D3B4/temp/uni-media/1754386753394.mp4",
"byteSize": 6423699,
"size": 6273.144
}
]
}
拍摄的照片
{
"type": "image",
"tempFiles": [
{
"fileType": "image",
"tempFilePath": "file:///storage/emulated/0/Android/data/com.tree.app/apps/__UNI__753D3B4/temp/uni-media/1754386764144.jpg",
"size": 2794037
}
]
}

选择的图片、视频

{
"type": "video,image",
"tempFiles": [
{
"height": 2160,
"thumbTempFilePath": "/storage/emulated/0/Android/data/com.tree.app/apps/__UNI__753D3B4/temp/uni-media/video_thumb_1754386790636.jpg",
"fileType": "video",
"duration": 5.87,
"width": 3840,
"tempFilePath": "content://media/picker/0/com.android.providers.media.photopicker/media/**********",
"byteSize": 38239442,
"size": 37343.203
},
{
"fileType": "image",
"tempFilePath": "file:///storage/emulated/0/Android/data/com.tree.app/apps/__UNI__753D3B4/temp/uni-media/1754386790485_1000007124.jpg",
"size": 2723485
}
]
}

文件存相册？

tif 地图影像加载

快速填充表单信息：

vm.value = {
    "type": "巨柏",
    "treeSpecies": "巨柏",
    "treeCode": "50049",
    "commonName": "雅鲁藏布江柏木",
    "latinName": "Cupressus gigantea",
    "family": "柏科",
    "genus": "柏木属",
    "species": "",
    "estimatedAge": "",
    "ownershipUnit": "",
    "batch": "",
    "location": {
        "x": "82.136386",
        "y": "44.104582"
    },
    "areaCode": "",
    "town": "",
    "village": "",
    "smallPlaceName": "",
    "altitude": "20",
    "healthStatus": "濒危",
    "protectionLevel": "",
    "protectionType": "国家一级",
    "isRareSpecies": "false",
    "treeHeight": "10",
    "crownWidth": "10",
    "underBranchHeight": "",
    "area": "",
    "quantity": "",
    "transplantPlan": "",
    "measurementDimensionType": "胸径",
    "measurementInfo": {
        "chestDiameter": "10",
        "groundDiameter": "",
        "distributionDiameter": "",
        "branchCount": "",
        "farthestDiameter": ""
    },
    "specs": "60",
    "growthEnvironment": "中等",
    "isTransplant": "",
    "notTransplant": "",
    "landType": "",
    "soilTexture": "",
    "slope": "急坡(35°〜44°)",
    "aspect": "西坡",
    "slopePosition": "山谷",
    "siteConditionDesc": "",
    "protectionMeasureType": "",
    "team": "张泽、张欢",
    "relocationProtection": "",
    "managementMeasures": "",
    "relocationPlace": "",
    "projectSchedule": "",
    "laborStatistics": "",
    "investmentEstimate": "",
    "historicalAnecdotes": "",
    "discoveryDate": "2025-08-12",
    "remarks": "",
    "multimedia": [
        {}
    ]
}


每次打包之前，给添加版本信息

定位：

613 任务已完成
614 水印生成抛出的异常

正在上传 7 条数据

3 条成功 第四条的时候 401 过期了

再上传，3 条就又上传了
还有，如果有纯修改的接口，走service不走uploadService的


定位方式切换



大数据量上传测试 
同步的时候，如果中途有失败的，那么强制退出重启后，成功的还在列表中显示着！

```高德返回的数据
{
    "coordsType": "gcj02",
    "address": {
        "country": "中国",
        "province": "陕西省",
        "city": "西安市",
        "district": "长安区",
        "street": "航飞路",
        "streetNum": "4455号",
        "poiName": "中天引控科技股份有限公司",
        "cityCode": "029"
    },
    "addresses": "陕西省西安市长安区航飞路4455号靠近中天引控科技股份有限公司",
    "coords": {
        "latitude": 34.135196,
        "longitude": 108.98654,
        "accuracy": 30,
        "altitude": 0,
        "heading": null,
        "speed": 0,
        "altitudeAccuracy": 0
    },
    "timestamp": 1755682573538
}
```

测试经纬度：
93.834042
29.121374